#!/usr/bin/env python3
"""
Script to download category images from CSV files.
Downloads banner and icon images for categories, sub_categories, and sub_sub_categories.
"""

import csv
import os
import logging
import requests
import time
from urllib.parse import urljoin, urlparse
from pathlib import Path
import hashlib
import re

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('category_download_log.txt'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CategoryImageDownloader:
    def __init__(self, base_cdn_url="https://djpw4cfh60y52.cloudfront.net/"):
        self.base_cdn_url = base_cdn_url
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # Configuration for different category types
        self.category_configs = {
            'categories': {
                'csv_file': 'categories.csv',
                'banner_folder': 'categories_banner',
                'icon_folder': 'categories_icon'
            },
            'sub_categories': {
                'csv_file': 'sub_categories.csv',
                'banner_folder': 'sub_categories_banner',
                'icon_folder': 'sub_categories_icon'
            },
            'sub_sub_categories': {
                'csv_file': 'sub_sub_categories.csv',
                'banner_folder': 'sub_sub_categories_banner',
                'icon_folder': 'sub_sub_categories_icon'
            }
        }
        
        # Create all required folders
        self.create_folders()
        
        # Statistics
        self.stats = {
            'total_rows': 0,
            'successful_downloads': 0,
            'skipped_existing': 0,
            'failed_downloads': 0,
            'invalid_urls': 0,
            'empty_paths': 0
        }
    
    def create_folders(self):
        """Create all required output folders"""
        for config in self.category_configs.values():
            Path(config['banner_folder']).mkdir(exist_ok=True)
            Path(config['icon_folder']).mkdir(exist_ok=True)
            logger.info(f"Created folders: {config['banner_folder']}, {config['icon_folder']}")
    
    def sanitize_filename(self, url):
        """Create a safe filename from complete URL"""
        if not url:
            return None

        # Use the complete URL as filename, but sanitize it for filesystem
        filename = url

        # Replace URL-unsafe characters with underscores
        filename = re.sub(r'[^\w\-_\.]', '_', filename)

        # Ensure it has an extension
        if not any(filename.lower().endswith(ext) for ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp']):
            filename += '.png'  # Default to PNG for category images

        # Limit filename length
        if len(filename) > 200:
            # Use hash for very long filenames
            hash_obj = hashlib.md5(url.encode())
            ext = filename.split('.')[-1] if '.' in filename else 'png'
            filename = hash_obj.hexdigest() + '.' + ext

        return filename
    
    def is_valid_url(self, url):
        """Check if URL is valid"""
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except Exception:
            return False
    
    def download_image(self, url, filepath):
        """Download image from URL and save to file"""
        try:
            response = self.session.get(url, timeout=30, stream=True)
            response.raise_for_status()
            
            # Check if response contains image data
            content_type = response.headers.get('content-type', '').lower()
            if not any(img_type in content_type for img_type in ['image/', 'application/octet-stream']):
                logger.warning(f"URL does not contain image data: {url}")
                return False
            
            # Save the image
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            
            logger.info(f"Downloaded: {filepath}")
            return True
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to download {url}: {e}")
            return False
        except IOError as e:
            logger.error(f"Failed to save {filepath}: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error downloading {url}: {e}")
            return False
    
    def process_image_column(self, row, column_name, output_folder, row_num, category_type):
        """Process a single image column (banner or icon)"""
        image_path = row.get(column_name, '').strip()
        
        # Skip if path is empty or NULL
        if not image_path or image_path.upper() == 'NULL':
            logger.debug(f"Row {row_num} ({category_type}): Empty {column_name}")
            self.stats['empty_paths'] += 1
            return
        
        # Create complete URL
        if image_path.startswith('http'):
            complete_url = image_path
        else:
            complete_url = urljoin(self.base_cdn_url, image_path)
        
        # Validate URL
        if not self.is_valid_url(complete_url):
            logger.warning(f"Row {row_num} ({category_type}): Invalid URL: {complete_url}")
            self.stats['invalid_urls'] += 1
            return
        
        # Create filename from complete URL
        filename = self.sanitize_filename(complete_url)
        if not filename:
            logger.warning(f"Row {row_num} ({category_type}): Could not create filename from: {complete_url}")
            self.stats['invalid_urls'] += 1
            return

        filepath = Path(output_folder) / filename
        
        # Check if file already exists
        if filepath.exists():
            logger.debug(f"Row {row_num} ({category_type}): File already exists: {filename}")
            self.stats['skipped_existing'] += 1
            return
        
        # Download the image
        if self.download_image(complete_url, filepath):
            self.stats['successful_downloads'] += 1
        else:
            self.stats['failed_downloads'] += 1
    
    def process_csv_file(self, category_type):
        """Process a single CSV file for the given category type"""
        config = self.category_configs[category_type]
        csv_file = config['csv_file']
        
        logger.info(f"Processing {category_type}: {csv_file}")
        
        if not os.path.exists(csv_file):
            logger.error(f"CSV file not found: {csv_file}")
            return
        
        try:
            with open(csv_file, 'r', encoding='utf-8') as file:
                # Detect delimiter
                sample = file.read(1024)
                file.seek(0)
                sniffer = csv.Sniffer()
                delimiter = sniffer.sniff(sample).delimiter
                
                reader = csv.DictReader(file, delimiter=delimiter)
                
                # Check if required columns exist
                required_columns = ['banner', 'icon']
                missing_columns = [col for col in required_columns if col not in reader.fieldnames]
                if missing_columns:
                    logger.error(f"Missing columns in {csv_file}: {missing_columns}")
                    return
                
                logger.info(f"Processing {category_type} with columns: {reader.fieldnames}")
                
                for row_num, row in enumerate(reader, start=2):  # Start from 2 (header is row 1)
                    self.stats['total_rows'] += 1
                    
                    try:
                        # Process banner image
                        self.process_image_column(
                            row, 'banner', config['banner_folder'], 
                            row_num, f"{category_type}_banner"
                        )
                        
                        # Process icon image
                        self.process_image_column(
                            row, 'icon', config['icon_folder'], 
                            row_num, f"{category_type}_icon"
                        )
                        
                        # Add small delay to be respectful to the server
                        time.sleep(0.1)
                        
                        # Log progress every 50 rows
                        if (row_num - 1) % 50 == 0:
                            logger.info(f"Processed {row_num - 1} rows from {category_type}...")
                    
                    except Exception as e:
                        logger.error(f"Error processing row {row_num} in {category_type}: {e}")
                        continue
        
        except Exception as e:
            logger.error(f"Error reading CSV file {csv_file}: {e}")
            return
    
    def process_all_categories(self):
        """Process all category CSV files"""
        logger.info("Starting to process all category CSV files")
        
        for category_type in self.category_configs.keys():
            logger.info(f"\n{'='*50}")
            logger.info(f"Processing {category_type.upper()}")
            logger.info(f"{'='*50}")
            
            self.process_csv_file(category_type)
        
        # Print final statistics
        self.print_statistics()
    
    def print_statistics(self):
        """Print download statistics"""
        logger.info("\n" + "=" * 60)
        logger.info("FINAL DOWNLOAD STATISTICS")
        logger.info("=" * 60)
        logger.info(f"Total rows processed: {self.stats['total_rows']}")
        logger.info(f"Successful downloads: {self.stats['successful_downloads']}")
        logger.info(f"Files already existed: {self.stats['skipped_existing']}")
        logger.info(f"Failed downloads: {self.stats['failed_downloads']}")
        logger.info(f"Invalid URLs: {self.stats['invalid_urls']}")
        logger.info(f"Empty/NULL paths: {self.stats['empty_paths']}")
        logger.info("=" * 60)

def main():
    """Main function"""
    downloader = CategoryImageDownloader()
    downloader.process_all_categories()

if __name__ == "__main__":
    main()
