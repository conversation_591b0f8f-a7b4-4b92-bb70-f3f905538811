#!/usr/bin/env python3
"""
Script to download images from CSV file for grocery items.
Reads CSV file and downloads images from CDN + thumbnail_image URLs.
"""

import csv
import os
import logging
import requests
import time
from urllib.parse import urljoin, urlparse
from pathlib import Path
import hashlib
import re

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('download_log.txt'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ImageDownloader:
    def __init__(self, csv_file, output_folder):
        self.csv_file = csv_file
        self.output_folder = Path(output_folder)
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # Create output folder if it doesn't exist
        self.output_folder.mkdir(exist_ok=True)
        
        # Statistics
        self.stats = {
            'total_rows': 0,
            'successful_downloads': 0,
            'skipped_existing': 0,
            'failed_downloads': 0,
            'invalid_urls': 0
        }
    
    def sanitize_filename(self, url):
        """Create a safe filename from URL"""
        # Remove protocol and replace special characters
        filename = re.sub(r'[^\w\-_\.]', '_', url)
        # Limit filename length
        if len(filename) > 200:
            # Use hash for very long URLs
            hash_obj = hashlib.md5(url.encode())
            filename = hash_obj.hexdigest() + '.jpg'
        elif not filename.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.webp')):
            filename += '.jpg'
        
        return filename
    
    def is_valid_url(self, url):
        """Check if URL is valid"""
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except Exception:
            return False
    
    def download_image(self, url, filename):
        """Download image from URL and save to file"""
        try:
            response = self.session.get(url, timeout=30, stream=True)
            response.raise_for_status()
            
            # Check if response contains image data
            content_type = response.headers.get('content-type', '').lower()
            if not any(img_type in content_type for img_type in ['image/', 'application/octet-stream']):
                logger.warning(f"URL does not contain image data: {url}")
                return False
            
            # Save the image
            filepath = self.output_folder / filename
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            
            logger.info(f"Downloaded: {filename}")
            return True
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to download {url}: {e}")
            return False
        except IOError as e:
            logger.error(f"Failed to save {filename}: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error downloading {url}: {e}")
            return False
    
    def process_csv(self):
        """Process the CSV file and download images"""
        logger.info(f"Starting to process CSV file: {self.csv_file}")
        
        try:
            with open(self.csv_file, 'r', encoding='utf-8') as file:
                # Detect delimiter
                sample = file.read(1024)
                file.seek(0)
                sniffer = csv.Sniffer()
                delimiter = sniffer.sniff(sample).delimiter
                
                reader = csv.DictReader(file, delimiter=delimiter)
                
                # Check if required columns exist
                if 'CDN' not in reader.fieldnames or 'thumbnail_image' not in reader.fieldnames:
                    logger.error("Required columns 'CDN' and 'thumbnail_image' not found in CSV")
                    return
                
                logger.info(f"Found columns: {reader.fieldnames}")
                
                for row_num, row in enumerate(reader, start=2):  # Start from 2 (header is row 1)
                    self.stats['total_rows'] += 1
                    
                    try:
                        cdn = row.get('CDN', '').strip()
                        thumbnail = row.get('thumbnail_image', '').strip()
                        
                        # Skip rows with empty CDN or thumbnail_image
                        if not cdn or not thumbnail:
                            logger.debug(f"Row {row_num}: Skipping due to empty CDN or thumbnail_image")
                            continue
                        
                        # Construct complete URL
                        if thumbnail.startswith('http'):
                            complete_url = thumbnail
                        else:
                            complete_url = urljoin(cdn, thumbnail)
                        
                        # Validate URL
                        if not self.is_valid_url(complete_url):
                            logger.warning(f"Row {row_num}: Invalid URL: {complete_url}")
                            self.stats['invalid_urls'] += 1
                            continue
                        
                        # Create filename from URL
                        filename = self.sanitize_filename(complete_url)
                        filepath = self.output_folder / filename
                        
                        # Check if file already exists
                        if filepath.exists():
                            logger.debug(f"Row {row_num}: File already exists: {filename}")
                            self.stats['skipped_existing'] += 1
                            continue
                        
                        # Download the image
                        if self.download_image(complete_url, filename):
                            self.stats['successful_downloads'] += 1
                        else:
                            self.stats['failed_downloads'] += 1
                        
                        # Add small delay to be respectful to the server
                        time.sleep(0.1)
                        
                        # Log progress every 100 rows
                        if self.stats['total_rows'] % 100 == 0:
                            logger.info(f"Processed {self.stats['total_rows']} rows...")
                    
                    except Exception as e:
                        logger.error(f"Error processing row {row_num}: {e}")
                        self.stats['failed_downloads'] += 1
                        continue
        
        except FileNotFoundError:
            logger.error(f"CSV file not found: {self.csv_file}")
            return
        except Exception as e:
            logger.error(f"Error reading CSV file: {e}")
            return
        
        # Print final statistics
        self.print_statistics()
    
    def print_statistics(self):
        """Print download statistics"""
        logger.info("=" * 50)
        logger.info("DOWNLOAD STATISTICS")
        logger.info("=" * 50)
        logger.info(f"Total rows processed: {self.stats['total_rows']}")
        logger.info(f"Successful downloads: {self.stats['successful_downloads']}")
        logger.info(f"Files already existed: {self.stats['skipped_existing']}")
        logger.info(f"Failed downloads: {self.stats['failed_downloads']}")
        logger.info(f"Invalid URLs: {self.stats['invalid_urls']}")
        logger.info("=" * 50)

def main():
    """Main function"""
    csv_file = "rozana images - Grocery & Staple.csv"
    output_folder = "grocery"
    
    # Check if CSV file exists
    if not os.path.exists(csv_file):
        logger.error(f"CSV file '{csv_file}' not found in current directory")
        return
    
    # Create downloader and process CSV
    downloader = ImageDownloader(csv_file, output_folder)
    downloader.process_csv()

if __name__ == "__main__":
    main()
